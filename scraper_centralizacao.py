#!/usr/bin/env python3
"""
Script para extrair conteúdo de todos os temas do site de Centralização
e consolidar em um arquivo Word.
"""

import requests
from bs4 import BeautifulSoup
import time
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
import re
import logging
from urllib.parse import urljoin, urlparse
import os

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CentralizacaoScraper:
    def __init__(self):
        self.base_url = "https://sites.google.com/view/centralizacao"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # Lista de temas para teste
        self.temas_teste = [
            "alienação-fiduciária",
            "ccb",
            "condomínio"
        ]

        # Lista completa será extraída automaticamente da página principal
        self.temas_completo = []
        self.temas = self.temas_teste

    def extrair_todos_temas(self):
        """Extrai automaticamente todos os links de temas da página principal."""
        try:
            logger.info("Extraindo lista de temas da página principal...")
            url = f"{self.base_url}/temas"
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Encontrar todos os links que apontam para temas
            tema_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if href and '/view/centralizacao/temas/' in href and href != '/view/centralizacao/temas':
                    # Extrair o slug do tema da URL
                    tema_slug = href.split('/temas/')[-1]
                    if tema_slug and tema_slug not in tema_links:
                        tema_links.append(tema_slug)

            if tema_links:
                self.temas_completo = sorted(tema_links)
                logger.info(f"Encontrados {len(self.temas_completo)} temas: {', '.join(self.temas_completo[:5])}...")
                return True
            else:
                logger.warning("Nenhum tema encontrado na página principal")
                return False

        except Exception as e:
            logger.error(f"Erro ao extrair temas da página principal: {e}")
            return False

    def fetch_page_content(self, tema):
        """Busca o conteúdo de uma página específica de tema."""
        url = f"{self.base_url}/temas/{tema}"

        try:
            logger.info(f"Buscando conteúdo de: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Extrair título da página
            title = self.extract_page_title(soup, tema)

            # Extrair conteúdo principal
            content = self.extract_main_content(soup)

            return {
                'titulo': title,
                'conteudo': content,
                'url': url
            }

        except requests.RequestException as e:
            logger.error(f"Erro ao buscar {url}: {e}")
            return None
        except Exception as e:
            logger.error(f"Erro inesperado ao processar {url}: {e}")
            return None

    def extract_page_title(self, soup, tema_slug):
        """Extrai o título da página."""
        # Tentar extrair do título da página
        title_tag = soup.find('title')
        if title_tag:
            title_text = title_tag.get_text().strip()
            # Remover "Centralização - " do início se existir
            if title_text.startswith('Centralização - '):
                return title_text[16:]
            elif title_text != 'Centralização':
                return title_text

        # Tentar extrair de h1, h2, etc.
        for tag in ['h1', 'h2', 'h3']:
            header = soup.find(tag)
            if header and header.get_text().strip():
                return header.get_text().strip()

        # Fallback: converter slug para título
        return tema_slug.replace('-', ' ').title()

    def extract_main_content(self, soup):
        """Extrai o conteúdo principal da página."""
        # Primeiro, tentar encontrar o conteúdo específico da página
        # Baseado no debug, o conteúdo está espalhado em divs

        # Remover elementos desnecessários
        for element in soup.find_all(['script', 'style', 'nav', 'header', 'footer', 'aside']):
            element.decompose()

        # Estratégia específica para Google Sites
        # O conteúdo principal geralmente está após o título da página

        # Procurar pelo título principal da página
        main_title = None
        for h_tag in ['h1', 'h2', 'h3']:
            title_elem = soup.find(h_tag)
            if title_elem and title_elem.get_text().strip():
                title_text = title_elem.get_text().strip()
                # Verificar se não é parte da navegação
                if not any(nav_word in title_text.lower() for nav_word in ['centralização', 'início', 'temas']):
                    main_title = title_elem
                    break

        content_parts = []

        if main_title:
            # Encontrar conteúdo após o título
            current = main_title.next_sibling
            while current:
                if hasattr(current, 'get_text'):
                    text = current.get_text().strip()
                    if text and len(text) > 10:
                        # Filtrar conteúdo de navegação
                        if not any(nav_word in text.lower() for nav_word in [
                            'google sites', 'report abuse', 'page details', 'skip to'
                        ]):
                            content_parts.append(text)
                current = current.next_sibling

        # Se não encontrou muito conteúdo, usar estratégia alternativa
        if len('\n'.join(content_parts)) < 200:
            # Procurar por divs com conteúdo substancial
            all_divs = soup.find_all('div')
            for div in all_divs:
                text = div.get_text().strip()
                if len(text) > 100 and len(text) < 5000:  # Conteúdo de tamanho razoável
                    # Verificar se não é navegação
                    if not any(nav_word in text.lower() for nav_word in [
                        'skip to', 'search this site', 'google sites', 'report abuse',
                        'início', 'temas', 'índice', 'instruções'
                    ]):
                        # Verificar se contém conteúdo útil
                        if any(keyword in text.lower() for keyword in [
                            'procedimento', 'artigo', 'lei', 'registro', 'emolumento',
                            'alienação', 'hipoteca', 'matrícula', 'cartório'
                        ]):
                            content_parts.append(text)
                            break

        # Juntar todo o conteúdo encontrado
        full_content = '\n\n'.join(content_parts)

        # Limpar e formatar
        full_content = re.sub(r'\n\s*\n\s*\n+', '\n\n', full_content)
        full_content = re.sub(r'[ \t]+', ' ', full_content)

        return full_content.strip()

    def extract_formatted_text(self, element):
        """Extrai texto formatado preservando estrutura básica."""
        if not element:
            return ""

        # Remover elementos de navegação e outros desnecessários
        for unwanted in element.find_all(['nav', 'aside', 'footer', 'header']):
            unwanted.decompose()

        # Remover listas que parecem ser navegação (muitos links)
        for ul in element.find_all(['ul', 'ol']):
            links = ul.find_all('a')
            if len(links) > 3:  # Se tem muitos links, provavelmente é navegação
                ul.decompose()

        text_parts = []

        # Processar elementos de forma hierárquica
        for elem in element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'div', 'li', 'span']):
            if not elem.get_text().strip():
                continue

            # Pular se é parte de navegação
            if any(nav_word in elem.get_text().lower() for nav_word in ['skip to', 'search this site', 'google sites', 'report abuse']):
                continue

            text = elem.get_text().strip()

            if elem.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                # Títulos com destaque
                text_parts.append(f"\n\n{'='*len(text)}\n{text}\n{'='*len(text)}\n")
            elif elem.name == 'p' and len(text) > 10:
                # Parágrafos
                text_parts.append(f"\n{text}\n")
            elif elem.name == 'li':
                # Itens de lista
                text_parts.append(f"• {text}\n")
            elif elem.name in ['div', 'span'] and len(text) > 20:
                # Divs e spans com conteúdo substancial
                text_parts.append(f"\n{text}\n")

        # Se não conseguiu extrair muito conteúdo, tentar método mais simples
        if len(''.join(text_parts).strip()) < 100:
            # Método alternativo: pegar todo o texto e limpar
            all_text = element.get_text()
            # Remover partes de navegação conhecidas
            unwanted_phrases = [
                'Search this site', 'Skip to main content', 'Skip to navigation',
                'Google Sites', 'Report abuse', 'Page details', 'Page updated',
                'Centralização', 'Início', 'Temas', 'Índice', 'Instruções'
            ]

            for phrase in unwanted_phrases:
                all_text = all_text.replace(phrase, '')

            # Limpar espaços excessivos
            all_text = re.sub(r'\s+', ' ', all_text).strip()

            if len(all_text) > len(''.join(text_parts).strip()):
                return all_text

        # Juntar e limpar texto
        content = ''.join(text_parts)
        content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)  # Remover linhas em branco excessivas
        content = re.sub(r'[ \t]+', ' ', content)  # Normalizar espaços

        return content.strip()

    def create_word_document(self, conteudos):
        """Cria documento Word com todo o conteúdo coletado."""
        doc = Document()
        
        # Título principal
        title = doc.add_heading('Centralização - Compilação de Temas', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Adicionar data de geração
        doc.add_paragraph(f"Documento gerado em: {time.strftime('%d/%m/%Y às %H:%M')}")
        doc.add_paragraph()
        
        # Índice
        doc.add_heading('Índice', level=1)
        for i, conteudo in enumerate(conteudos, 1):
            if conteudo:
                doc.add_paragraph(f"{i}. {conteudo['titulo']}")
        
        doc.add_page_break()
        
        # Conteúdo de cada tema
        for i, conteudo in enumerate(conteudos, 1):
            if conteudo:
                # Título do tema
                doc.add_heading(f"{i}. {conteudo['titulo']}", level=1)
                
                # URL de origem
                doc.add_paragraph(f"Fonte: {conteudo['url']}")
                doc.add_paragraph()
                
                # Conteúdo
                if conteudo['conteudo']:
                    paragraphs = conteudo['conteudo'].split('\n\n')
                    for paragraph in paragraphs:
                        if paragraph.strip():
                            doc.add_paragraph(paragraph.strip())
                else:
                    doc.add_paragraph("Conteúdo não disponível ou não foi possível extrair.")
                
                # Quebra de página entre temas
                if i < len(conteudos):
                    doc.add_page_break()
        
        return doc

    def set_modo_completo(self, completo=True):
        """Define se deve usar todos os temas ou apenas alguns para teste."""
        if completo:
            # Tentar extrair temas automaticamente se ainda não foi feito
            if not self.temas_completo:
                if not self.extrair_todos_temas():
                    logger.warning("Falha ao extrair temas automaticamente, usando lista padrão")
                    # Lista de fallback baseada na sua lista
                    self.temas_completo = [
                        "alienação-fiduciária", "alteração-caráter-jurídico-da-propriedade",
                        "alteração-da-razão-social", "cadastro-fiscal", "ccb", "cci", "cédulas",
                        "certidão-simplificada", "cessão-de-direitos", "cindibilidade", "cnd",
                        "condomínio", "consolidação", "construção", "custas", "erro-evidente",
                        "exame-e-cálculo", "funrejus", "fusão-e-unificação", "hipoteca", "imóvel",
                        "impostos", "incomunicabilidade-e-outras", "incorporação", "indisponibilidade",
                        "instituição", "integralização-de-capital", "itbi", "justiça-gratuita",
                        "leilão", "livro-5", "locação", "normativa", "ônus", "pacto-antenupcial",
                        "partilha", "patrimônio-de-afetação", "penhora", "permuta", "procuração",
                        "qualificação-das-partes", "reconhecimento-de-firma", "regime-de-bens",
                        "subdivisão", "união-estável", "usufruto", "venda-e-compra"
                    ]

            self.temas = self.temas_completo
            logger.info(f"Modo COMPLETO ativado - {len(self.temas)} temas")
        else:
            self.temas = self.temas_teste
            logger.info(f"Modo TESTE ativado - {len(self.temas)} temas")

    def run(self, modo_completo=False):
        """Executa o processo completo de scraping e geração do documento."""
        self.set_modo_completo(modo_completo)

        logger.info("Iniciando processo de scraping...")

        conteudos = []
        total_temas = len(self.temas)

        for i, tema in enumerate(self.temas, 1):
            logger.info(f"Processando tema {i}/{total_temas}: {tema}")

            conteudo = self.fetch_page_content(tema)
            conteudos.append(conteudo)

            # Pausa entre requisições para ser respeitoso com o servidor
            time.sleep(2)

        # Filtrar conteúdos válidos
        conteudos_validos = [c for c in conteudos if c is not None]

        logger.info(f"Coletados {len(conteudos_validos)} temas de {total_temas} total")

        # Criar documento Word
        logger.info("Criando documento Word...")
        doc = self.create_word_document(conteudos_validos)

        # Salvar documento
        modo_str = "completo" if modo_completo else "teste"
        filename = f"centralizacao_temas_{modo_str}_{time.strftime('%Y%m%d_%H%M%S')}.docx"
        doc.save(filename)

        logger.info(f"Documento salvo como: {filename}")
        logger.info(f"Total de temas processados: {len(conteudos_validos)}")

        return filename

if __name__ == "__main__":
    import sys

    # Verificar argumentos da linha de comando
    modo_completo = False
    if len(sys.argv) > 1 and sys.argv[1].lower() in ['completo', 'full', '--completo', '--full']:
        modo_completo = True

    scraper = CentralizacaoScraper()

    if not modo_completo:
        print("\n🔍 MODO TESTE ATIVADO")
        print("📝 Processando apenas 3 temas para teste")
        print("💡 Para processar todos os temas, execute: python scraper_centralizacao.py completo")
        print()
    else:
        print("\n🚀 MODO COMPLETO ATIVADO")
        print("📝 Processando todos os 47 temas")
        print("⏱️  Este processo pode demorar vários minutos...")
        print()

    try:
        filename = scraper.run(modo_completo=modo_completo)
        print(f"\n✅ Processo concluído com sucesso!")
        print(f"📄 Arquivo gerado: {filename}")
        print(f"📍 Localização: {os.path.abspath(filename)}")

        if not modo_completo:
            print("\n💡 Para processar todos os temas, execute:")
            print("   python scraper_centralizacao.py completo")

    except Exception as e:
        logger.error(f"Erro durante execução: {e}")
        print(f"\n❌ Erro durante a execução: {e}")
