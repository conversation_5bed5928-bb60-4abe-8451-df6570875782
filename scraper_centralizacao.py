#!/usr/bin/env python3
"""
Script para extrair conteúdo de todos os temas do site de Centralização
e consolidar em um arquivo Word.
"""

import requests
from bs4 import BeautifulSoup
import time
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
import re
import logging
from urllib.parse import urljoin, urlparse
import os

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CentralizacaoScraper:
    def __init__(self):
        self.base_url = "https://sites.google.com/view/centralizacao"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Lista de todos os temas encontrados na página principal
        # Para teste inicial, usando apenas alguns temas
        self.temas_teste = [
            "alienação-fiduciária",
            "ccb",
            "condomínio"
        ]

        self.temas_completo = [
            "alienação-fiduciária",
            "alteração-caráter-jurídico-da-propriedade",
            "alteração-da-razão-social",
            "cadastro-fiscal",
            "ccb",
            "cci",
            "cédulas",
            "certidão-simplificada",
            "cessão-de-direitos",
            "cindibilidade",
            "cnd",
            "condomínio",
            "consolidação",
            "construção",
            "custas",
            "erro-evidente",
            "exame-e-cálculo",
            "funrejus",
            "fusão-e-unificação",
            "hipoteca",
            "imóvel",
            "impostos",
            "incomunicabilidade-e-outras",
            "incorporação",
            "indisponibilidade",
            "instituição",
            "integralização-de-capital",
            "itbi",
            "justiça-gratuita",
            "leilão",
            "livro-5",
            "locação",
            "normativa",
            "ônus",
            "pacto-antenupcial",
            "partilha",
            "patrimônio-de-afetação",
            "penhora",
            "permuta",
            "procuração",
            "qualificação-das-partes",
            "reconhecimento-de-firma",
            "regime-de-bens",
            "subdivisão",
            "união-estável",
            "usufruto",
            "venda-e-compra"
        ]

        # Usar lista de teste por padrão, pode ser alterado
        self.temas = self.temas_teste

    def fetch_page_content(self, tema):
        """Busca o conteúdo de uma página específica de tema."""
        url = f"{self.base_url}/temas/{tema}"
        
        try:
            logger.info(f"Buscando conteúdo de: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extrair título
            title_element = soup.find('h1') or soup.find('title')
            title = title_element.get_text().strip() if title_element else tema.replace('-', ' ').title()
            
            # Extrair conteúdo principal
            content_div = soup.find('div', {'class': 'sites-canvas-main'}) or soup.find('main')
            
            if not content_div:
                # Tentar outras estratégias para encontrar conteúdo
                content_div = soup.find('div', {'id': 'sites-canvas'}) or soup.body
            
            content = ""
            if content_div:
                # Remover elementos desnecessários
                for element in content_div.find_all(['script', 'style', 'nav', 'header', 'footer']):
                    element.decompose()
                
                # Extrair texto preservando estrutura
                content = self.extract_formatted_text(content_div)
            
            return {
                'titulo': title,
                'conteudo': content,
                'url': url
            }
            
        except requests.RequestException as e:
            logger.error(f"Erro ao buscar {url}: {e}")
            return None
        except Exception as e:
            logger.error(f"Erro inesperado ao processar {url}: {e}")
            return None

    def extract_formatted_text(self, element):
        """Extrai texto formatado preservando estrutura básica."""
        text_parts = []
        
        for child in element.descendants:
            if child.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                text_parts.append(f"\n\n{child.get_text().strip()}\n")
            elif child.name == 'p':
                text_parts.append(f"\n{child.get_text().strip()}\n")
            elif child.name in ['li']:
                text_parts.append(f"• {child.get_text().strip()}\n")
            elif child.name == 'br':
                text_parts.append("\n")
            elif child.string and child.parent.name not in ['script', 'style']:
                text_parts.append(child.string)
        
        # Limpar e formatar texto
        content = ''.join(text_parts)
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)  # Remover linhas em branco excessivas
        content = re.sub(r'[ \t]+', ' ', content)  # Normalizar espaços
        
        return content.strip()

    def create_word_document(self, conteudos):
        """Cria documento Word com todo o conteúdo coletado."""
        doc = Document()
        
        # Título principal
        title = doc.add_heading('Centralização - Compilação de Temas', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Adicionar data de geração
        doc.add_paragraph(f"Documento gerado em: {time.strftime('%d/%m/%Y às %H:%M')}")
        doc.add_paragraph()
        
        # Índice
        doc.add_heading('Índice', level=1)
        for i, conteudo in enumerate(conteudos, 1):
            if conteudo:
                doc.add_paragraph(f"{i}. {conteudo['titulo']}")
        
        doc.add_page_break()
        
        # Conteúdo de cada tema
        for i, conteudo in enumerate(conteudos, 1):
            if conteudo:
                # Título do tema
                doc.add_heading(f"{i}. {conteudo['titulo']}", level=1)
                
                # URL de origem
                doc.add_paragraph(f"Fonte: {conteudo['url']}")
                doc.add_paragraph()
                
                # Conteúdo
                if conteudo['conteudo']:
                    paragraphs = conteudo['conteudo'].split('\n\n')
                    for paragraph in paragraphs:
                        if paragraph.strip():
                            doc.add_paragraph(paragraph.strip())
                else:
                    doc.add_paragraph("Conteúdo não disponível ou não foi possível extrair.")
                
                # Quebra de página entre temas
                if i < len(conteudos):
                    doc.add_page_break()
        
        return doc

    def set_modo_completo(self, completo=True):
        """Define se deve usar todos os temas ou apenas alguns para teste."""
        if completo:
            self.temas = self.temas_completo
            logger.info(f"Modo COMPLETO ativado - {len(self.temas)} temas")
        else:
            self.temas = self.temas_teste
            logger.info(f"Modo TESTE ativado - {len(self.temas)} temas")

    def run(self, modo_completo=False):
        """Executa o processo completo de scraping e geração do documento."""
        self.set_modo_completo(modo_completo)

        logger.info("Iniciando processo de scraping...")

        conteudos = []
        total_temas = len(self.temas)

        for i, tema in enumerate(self.temas, 1):
            logger.info(f"Processando tema {i}/{total_temas}: {tema}")

            conteudo = self.fetch_page_content(tema)
            conteudos.append(conteudo)

            # Pausa entre requisições para ser respeitoso com o servidor
            time.sleep(2)

        # Filtrar conteúdos válidos
        conteudos_validos = [c for c in conteudos if c is not None]

        logger.info(f"Coletados {len(conteudos_validos)} temas de {total_temas} total")

        # Criar documento Word
        logger.info("Criando documento Word...")
        doc = self.create_word_document(conteudos_validos)

        # Salvar documento
        modo_str = "completo" if modo_completo else "teste"
        filename = f"centralizacao_temas_{modo_str}_{time.strftime('%Y%m%d_%H%M%S')}.docx"
        doc.save(filename)

        logger.info(f"Documento salvo como: {filename}")
        logger.info(f"Total de temas processados: {len(conteudos_validos)}")

        return filename

if __name__ == "__main__":
    import sys

    # Verificar argumentos da linha de comando
    modo_completo = False
    if len(sys.argv) > 1 and sys.argv[1].lower() in ['completo', 'full', '--completo', '--full']:
        modo_completo = True

    scraper = CentralizacaoScraper()

    if not modo_completo:
        print("\n🔍 MODO TESTE ATIVADO")
        print("📝 Processando apenas 3 temas para teste")
        print("💡 Para processar todos os temas, execute: python scraper_centralizacao.py completo")
        print()
    else:
        print("\n🚀 MODO COMPLETO ATIVADO")
        print("📝 Processando todos os 47 temas")
        print("⏱️  Este processo pode demorar vários minutos...")
        print()

    try:
        filename = scraper.run(modo_completo=modo_completo)
        print(f"\n✅ Processo concluído com sucesso!")
        print(f"📄 Arquivo gerado: {filename}")
        print(f"📍 Localização: {os.path.abspath(filename)}")

        if not modo_completo:
            print("\n💡 Para processar todos os temas, execute:")
            print("   python scraper_centralizacao.py completo")

    except Exception as e:
        logger.error(f"Erro durante execução: {e}")
        print(f"\n❌ Erro durante a execução: {e}")
