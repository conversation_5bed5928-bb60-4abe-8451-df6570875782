@echo off
echo ========================================
echo  Scraper de Centralizacao - COMPLETO
echo ========================================
echo.

echo Verificando se Python esta instalado...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado. Por favor, instale Python 3.7 ou superior.
    pause
    exit /b 1
)

echo Python encontrado!
echo.

echo Instalando dependencias...
pip install -r requirements.txt

if errorlevel 1 (
    echo ERRO: Falha ao instalar dependencias.
    pause
    exit /b 1
)

echo.
echo Dependencias instaladas com sucesso!
echo.

echo Iniciando scraper em modo COMPLETO...
echo PROCESSANDO TODOS OS 47 TEMAS
echo ATENCAO: Este processo pode demorar varios minutos.
echo.

python scraper_centralizacao.py completo

echo.
echo Processo finalizado!
pause
