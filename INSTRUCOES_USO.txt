========================================
SCRAPER DE CENTRALIZACAO - INSTRUCOES
========================================

RESUMO:
Este script extrai automaticamente o conteudo de todos os temas do site 
https://sites.google.com/view/centralizacao/temas/ e gera um arquivo Word consolidado.

FORMAS DE EXECUCAO:

1. MODO FACIL (Windows):
   - Execute: run_scraper.bat
   - Escolha entre modo TESTE (3 temas) ou COMPLETO (47 temas)
   
   OU
   
   - Execute: run_completo.bat (processa todos os 47 temas diretamente)

2. MODO MANUAL:
   - Modo TESTE: python scraper_centralizacao.py
   - Modo COMPLETO: python scraper_centralizacao.py completo

ARQUIVOS GERADOS:
- Modo TESTE: centralizacao_temas_teste_YYYYMMDD_HHMMSS.docx
- Modo COMPLETO: centralizacao_temas_completo_YYYYMMDD_HHMMSS.docx

TEMPO ESTIMADO:
- Modo TESTE: ~30 segundos (3 temas)
- Modo COMPLETO: ~5-10 minutos (47 temas)

REQUISITOS:
- Python 3.7 ou superior
- Conexao com internet
- As dependencias sao instaladas automaticamente

CONTEUDO DO DOCUMENTO WORD:
1. Pagina de titulo com data de geracao
2. Indice com todos os temas
3. Cada tema em secao separada com:
   - Titulo do tema
   - URL de origem
   - Conteudo extraido
   - Quebra de pagina

TEMAS INCLUIDOS (47 total):
1. Alienacao Fiduciaria
2. Alteracao Carater Juridico da Propriedade
3. Alteracao da Razao Social
4. Cadastro Fiscal
5. CCB
6. CCI
7. Cedulas
8. Certidao Simplificada
9. Cessao de Direitos
10. Cindibilidade
11. CND
12. Condominio
13. Consolidacao
14. Construcao
15. Custas
16. Erro Evidente
17. Exame e Calculo
18. Funrejus
19. Fusao e Unificacao
20. Hipoteca
21. Imovel
22. Impostos
23. Incomunicabilidade e Outras
24. Incorporacao
25. Indisponibilidade
26. Instituicao
27. Integralizacao de Capital
28. ITBI
29. Justica Gratuita
30. Leilao
31. Livro 5
32. Locacao
33. Normativa
34. Onus
35. Pacto Antenupcial
36. Partilha
37. Patrimonio de Afetacao
38. Penhora
39. Permuta
40. Procuracao
41. Qualificacao das Partes
42. Reconhecimento de Firma
43. Regime de Bens
44. Subdivisao
45. Uniao Estavel
46. Usufruto
47. Venda e Compra

OBSERVACOES:
- O script inclui pausas entre requisicoes para ser respeitoso com o servidor
- Algumas paginas podem nao estar acessiveis temporariamente
- O processo e automatico e nao requer intervencao manual
- Logs detalhados sao exibidos durante a execucao

SOLUCAO DE PROBLEMAS:
- Se der erro de Python nao encontrado: instale Python 3.7+
- Se der erro de dependencias: execute "pip install -r requirements.txt"
- Se der erro de conexao: verifique sua internet

Para mais detalhes, consulte o arquivo README.md
