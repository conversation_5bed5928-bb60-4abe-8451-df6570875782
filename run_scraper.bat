@echo off
echo ========================================
echo  Scraper de Centralizacao - Temas
echo ========================================
echo.

echo Verificando se Python esta instalado...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado. Por favor, instale Python 3.7 ou superior.
    pause
    exit /b 1
)

echo Python encontrado!
echo.

echo Instalando dependencias...
pip install -r requirements.txt

if errorlevel 1 (
    echo ERRO: Falha ao instalar dependencias.
    pause
    exit /b 1
)

echo.
echo Dependencias instaladas com sucesso!
echo.

echo Escolha o modo de execucao:
echo 1. Modo TESTE (3 temas - rapido)
echo 2. Modo COMPLETO (47 temas - pode demorar varios minutos)
echo.
set /p choice="Digite 1 ou 2: "

if "%choice%"=="1" (
    echo.
    echo Iniciando scraper em modo TESTE...
    python scraper_centralizacao.py
) else if "%choice%"=="2" (
    echo.
    echo Iniciando scraper em modo COMPLETO...
    echo ATENCAO: Este processo pode demorar varios minutos.
    echo.
    python scraper_centralizacao.py completo
) else (
    echo.
    echo Opcao invalida. Executando modo TESTE por padrao...
    python scraper_centralizacao.py
)

echo.
echo Processo finalizado!
pause
