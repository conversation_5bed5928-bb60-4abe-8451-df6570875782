# Scraper de Centralização - Temas

Este script extrai automaticamente o conteúdo de todos os temas do site https://sites.google.com/view/centralizacao/temas/ e consolida tudo em um único arquivo Word.

## 📋 Funcionalidades

- ✅ **Extração Automática** - Detecta e extrai todos os 47 temas disponíveis
- ✅ **Conteúdo <PERSON>mpleto** - Captura procedimentos, artigos de lei, enunciados e instruções
- ✅ **Formatação Preservada** - Mantém títulos, parágrafos e estrutura do texto
- ✅ **Documento Word Organizado** - Gera arquivo .docx profissional
- ✅ **Índice Automático** - Lista todos os temas para navegação fácil
- ✅ **URLs de Origem** - Cada tema inclui link da fonte original
- ✅ **Rate Limiting** - Pausas entre requisições para respeitar o servidor
- ✅ **Do<PERSON>** - Teste (3 temas) e Completo (47 temas)
- ✅ **Logging Detalhado** - Acompanhamento completo do processo
- ✅ **Tratamento de Erros** - Continua funcionando mesmo se algumas páginas falharem

## 🚀 Como usar

### Opção 1: Execução Automática (Windows)

#### Modo Interativo
1. Execute o arquivo `run_scraper.bat`
2. Escolha entre:
   - **Modo TESTE**: Processa apenas 3 temas (rápido para testar)
   - **Modo COMPLETO**: Processa todos os 47 temas

#### Modo Completo Direto
1. Execute o arquivo `run_completo.bat` para processar todos os temas diretamente

### Opção 2: Execução Manual

#### Pré-requisitos
- Python 3.7 ou superior
- pip (gerenciador de pacotes Python)

#### Instalação das dependências
```bash
pip install -r requirements.txt
```

#### Execução

**Modo Teste (3 temas):**
```bash
python scraper_centralizacao.py
```

**Modo Completo (47 temas):**
```bash
python scraper_centralizacao.py completo
```

## 📦 Dependências

- `requests` - Para fazer requisições HTTP
- `beautifulsoup4` - Para parsing HTML
- `python-docx` - Para criar documentos Word
- `lxml` - Parser XML/HTML (usado pelo BeautifulSoup)

## 📄 Saída

O script gera um arquivo Word com nome no formato:
- **Modo Teste**: `centralizacao_temas_teste_YYYYMMDD_HHMMSS.docx`
- **Modo Completo**: `centralizacao_temas_completo_YYYYMMDD_HHMMSS.docx`

### Estrutura do documento:
1. **Página de título** - Com data de geração
2. **Índice** - Lista de todos os temas
3. **Conteúdo** - Cada tema em uma seção separada com:
   - Título do tema
   - URL de origem
   - Conteúdo extraído
   - Quebra de página entre temas

## 🔧 Configurações

### Modificar lista de temas
Para adicionar ou remover temas, edite a lista `self.temas` no arquivo `scraper_centralizacao.py`.

### Ajustar tempo entre requisições
Modifique a linha `time.sleep(2)` no método `run()` para alterar o intervalo entre requisições.

### Personalizar formatação do Word
Edite o método `create_word_document()` para modificar a formatação do documento final.

## 📊 Temas incluídos

O script extrai conteúdo dos seguintes 47 temas:

1. Alienação Fiduciária
2. Alteração Caráter Jurídico da Propriedade
3. Alteração da Razão Social
4. Cadastro Fiscal
5. CCB
6. CCI
7. Cédulas
8. Certidão Simplificada
9. Cessão de Direitos
10. Cindibilidade
11. CND
12. Condomínio
13. Consolidação
14. Construção
15. Custas
16. Erro Evidente
17. Exame e Cálculo
18. Funrejus
19. Fusão e Unificação
20. Hipoteca
21. Imóvel
22. Impostos
23. Incomunicabilidade e Outras
24. Incorporação
25. Indisponibilidade
26. Instituição
27. Integralização de Capital
28. ITBI
29. Justiça Gratuita
30. Leilão
31. Livro 5
32. Locação
33. Normativa
34. Ônus
35. Pacto Antenupcial
36. Partilha
37. Patrimônio de Afetação
38. Penhora
39. Permuta
40. Procuração
41. Qualificação das Partes
42. Reconhecimento de Firma
43. Regime de Bens
44. Subdivisão
45. União Estável
46. Usufruto
47. Venda e Compra

## ⚠️ Observações

- O processo pode demorar vários minutos devido ao grande número de páginas
- O script inclui pausas entre requisições para ser respeitoso com o servidor
- Algumas páginas podem não estar acessíveis ou ter conteúdo limitado
- O script tenta diferentes estratégias para extrair conteúdo de páginas com estruturas variadas

## 🐛 Solução de problemas

### Erro de conexão
- Verifique sua conexão com a internet
- Algumas páginas podem estar temporariamente indisponíveis

### Erro de dependências
- Execute: `pip install --upgrade pip`
- Reinstale as dependências: `pip install -r requirements.txt --force-reinstall`

### Documento Word não abre
- Verifique se tem Microsoft Word ou LibreOffice instalado
- O arquivo é compatível com Word 2007 ou superior

## 📝 Log

O script gera logs detalhados durante a execução, incluindo:
- Progresso do download de cada tema
- Erros encontrados
- Estatísticas finais

## 🤝 Contribuições

Para melhorias ou correções, edite diretamente os arquivos conforme necessário.
